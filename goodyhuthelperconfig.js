// Frida script for auto-collecting ruins in Unity IL2CPP game
// Based on GoodyHutHelper class analysis

Java.perform(function() {
    console.log("[+] Starting Ruins Auto-Collector");
    
    // Wait for libil2cpp.so to load
    console.log("[*] Waiting for libil2cpp.so to load...");
    var il2cpp = null;
    var moduleCheckInterval = null;
    var maxWaitTime = 60000; // 60 seconds maximum wait
    var startTime = Date.now();

    function checkForIL2CPP() {
        Process.enumerateModules().forEach(function(module) {
            if (module.name === "libil2cpp.so") {
                il2cpp = module;
                console.log("[+] Found libil2cpp.so at: " + module.base + " (size: " + (module.size/1024/1024).toFixed(1) + "MB)");
                return;
            }
        });

        if (il2cpp) {
            clearInterval(moduleCheckInterval);
            console.log("[+] libil2cpp.so loaded successfully!");
            initializeHooks();
        } else {
            var elapsed = Date.now() - startTime;
            if (elapsed > maxWaitTime) {
                clearInterval(moduleCheckInterval);
                console.log("[-] Timeout waiting for libil2cpp.so to load after " + (maxWaitTime/1000) + " seconds");
                console.log("[-] Available modules:");
                Process.enumerateModules().forEach(function(module) {
                    if (module.size > 5000000) { // > 5MB
                        console.log("    " + module.name + " (" + (module.size/1024/1024).toFixed(1) + "MB)");
                    }
                });
                console.log("[-] Use rpc.exports.setModule('module_name.so') to manually set the correct module");
                return;
            }

            if (elapsed % 5000 < 500) { // Log every 5 seconds
                console.log("[*] Still waiting for libil2cpp.so... (" + (elapsed/1000).toFixed(1) + "s elapsed)");
            }
        }
    }

    // Check immediately
    checkForIL2CPP();

    // If not found, check every 500ms
    if (!il2cpp) {
        moduleCheckInterval = setInterval(checkForIL2CPP, 500);
    } else {
        initializeHooks();
    }

    function initializeHooks() {
        if (!il2cpp || il2cpp.name === "MANUAL_OVERRIDE_NEEDED") {
            console.log("[-] Cannot initialize hooks - no valid IL2CPP module");
            return;
        }

        console.log("[+] Using module: " + il2cpp.name + " at base: " + il2cpp.base);

        // Calculate actual addresses using module base + RVA offsets
        var canCollectAddr = il2cpp.base.add(0x209D258);  // CanCollect() RVA
        var finishCollectAddr = il2cpp.base.add(0x209B924);  // FinishCollect() RVA

        console.log("[*] CanCollect address: " + canCollectAddr);
        console.log("[*] FinishCollect address: " + finishCollectAddr);
    var stateMachineOffset = 0x18;  // m_stateMachine field offset
    
    // State constants
    var STATE_CLEANING_UP = "cleaning_up";
    
    // Hook CanCollect to auto-trigger collection
    Interceptor.attach(canCollectAddr, {
        onEnter: function(args) {
            this.thisPtr = this.context.x0;  // 'this' pointer (GoodyHutHelper instance)
            console.log("[*] CanCollect() called on GoodyHutHelper: " + this.thisPtr);
        },
        
        onLeave: function(retval) {
            var canCollect = retval.toInt32();
            console.log("[*] CanCollect() returned: " + canCollect);
            
            if (canCollect === 1) {  // true in IL2CPP
                console.log("[+] Ruins can be collected! Auto-triggering...");
                
                try {
                    // Call FinishCollect() on the same instance
                    var finishCollectFunc = new NativeFunction(finishCollectAddr, 'void', ['pointer']);
                    finishCollectFunc(this.thisPtr);
                    console.log("[+] FinishCollect() executed successfully");
                    
                    // Update state to STATE_CLEANING_UP
                    updateEntityState(this.thisPtr, STATE_CLEANING_UP);
                    
                } catch (e) {
                    console.log("[-] Error executing FinishCollect(): " + e);
                }
            }
        }
    });
    
    // Helper function to update entity state and trigger cleanup
    function updateEntityState(goodyHutPtr, newState) {
        try {
            // Get StateMachine pointer (offset 0x18)
            var stateMachinePtr = goodyHutPtr.add(stateMachineOffset).readPointer();

            if (!stateMachinePtr.isNull()) {
                console.log("[*] Updating state to: " + newState);

                // Find il2cpp_string_new to create state string
                var stringNewFunc = Module.findExportByName("libil2cpp.so", "il2cpp_string_new");
                if (stringNewFunc) {
                    var createString = new NativeFunction(stringNewFunc, 'pointer', ['pointer']);
                    var stateString = createString(Memory.allocUtf8String(newState));

                    // You would need to find the StateMachine's SetState method
                    // This is a simplified example - actual implementation depends on StateMachine structure
                    console.log("[+] State string created: " + stateString);
                }

                // If state is cleaning up, trigger automatic selling after a short delay
                if (newState === STATE_CLEANING_UP) {
                    setTimeout(function() {
                        try {
                            console.log("[*] Attempting automatic ruin selling...");
                            var sellRuinsAddr = il2cpp.base.add(0x209DE3C);  // SellRuins() RVA
                            var sellRuinsFunc = new NativeFunction(sellRuinsAddr, 'void', ['pointer']);
                            sellRuinsFunc(goodyHutPtr);
                            console.log("[+] SellRuins() executed for automatic cleanup");
                        } catch (e) {
                            console.log("[-] Error in automatic ruin selling: " + e);
                        }
                    }, 1000);  // 1 second delay
                }
            }
        } catch (e) {
            console.log("[-] Error updating state: " + e);
        }
    }
    
    // Two-Phase Batch Processing System for Ruins Collection
    var BATCH_SIZE = 1;  // Process 15 instances per batch
    var BATCH_INTERVAL = 30;  // 3 seconds between batches
    var RETRY_LIMIT = 3;  // Maximum retry attempts per instance
    var DISCOVERY_TIMEOUT = 10000;  // 10 seconds without new discoveries to complete Phase 1

    // Phase tracking
    var currentPhase = 1;  // 1 = Discovery, 2 = Collection
    var lastDiscoveryTime = 0;
    var discoveryCompleteTime = 0;
    var totalDiscoveryScans = 0;

    // Data structures
    var discoveredInstances = new Map();  // instanceId -> {ptr, discoveryTime, retryCount}
    var processedInstances = new Set();   // Successfully processed instances
    var failedInstances = new Map();      // instanceId -> {ptr, lastFailTime, retryCount}
    var currentBatch = [];
    var batchNumber = 0;
    var isProcessingBatch = false;

    // Phase 1: Complete Discovery Scan
    var updateAddr = il2cpp.base.add(0x209CF60);  // Update() RVA
    var updateCallCount = 0;

    console.log("[+] ===== PHASE 1: DISCOVERY SCAN STARTED =====");
    console.log("[*] Scanning for ALL collectible GoodyHutHelper instances...");
    console.log("[*] Discovery will complete after 10 seconds with no new findings");

    Interceptor.attach(updateAddr, {
        onEnter: function(args) {
            updateCallCount++;
            totalDiscoveryScans++;

            // REAL-TIME COLLECTION: Process instances immediately when discovered
            if (updateCallCount % 15 === 0) {
                var thisPtr = this.context.x0;
                var instanceId = thisPtr.toString();

                try {
                    // Call CanCollect() to check if ready
                    var canCollectFunc = new NativeFunction(canCollectAddr, 'int', ['pointer']);
                    var result = canCollectFunc(thisPtr);

                    if (result === 1) {
                        // Check if we've already processed this instance
                        if (!processedInstances.has(instanceId)) {
                            console.log("[+] REAL-TIME: Found collectible ruins at: " + thisPtr + " - Processing immediately");

                            // Process immediately while pointer is fresh and valid
                            try {
                                // Get config and set cleanUp flag
                                var configAddr = il2cpp.base.add(0x209B54C);  // Config() RVA
                                var configFunc = new NativeFunction(configAddr, 'pointer', ['pointer']);
                                var configPtr = configFunc(thisPtr);

                                if (configPtr && !configPtr.isNull() && !configPtr.equals(ptr(0))) {
                                    try {
                                        var cleanUpOffset = 0x30;
                                        configPtr.add(cleanUpOffset).writeU8(1);
                                        console.log("[+] Set cleanUp flag for: " + instanceId);
                                    } catch (e) {
                                        console.log("[-] Error setting cleanUp flag: " + e);
                                    }
                                }

                                // Execute collection immediately
                                var finishCollectFunc = new NativeFunction(finishCollectAddr, 'void', ['pointer']);
                                finishCollectFunc(thisPtr);

                                // Mark as processed
                                processedInstances.add(instanceId);
                                console.log("[+] REAL-TIME SUCCESS: Collected ruins at " + thisPtr + " (Total processed: " + processedInstances.size + ")");

                            } catch (e) {
                                console.log("[-] REAL-TIME FAILED: Error processing " + instanceId + ": " + e);

                                // Add to failed instances for potential retry
                                failedInstances.set(instanceId, {
                                    ptr: thisPtr,
                                    lastFailTime: Date.now(),
                                    retryCount: 1
                                });
                            }
                        }
                    }
                } catch (e) {
                    // Silently ignore errors during discovery to avoid spam
                }
            }

            // Phase 2: Collection processing happens in batch timer, not here
        }
    });

    // Function to complete discovery phase and start collection
    function completeDiscoveryPhase() {
        currentPhase = 2;
        discoveryCompleteTime = Date.now();

        console.log("[+] ===== PHASE 1: DISCOVERY SCAN COMPLETED =====");
        console.log("[+] Total collectible instances discovered: " + discoveredInstances.size);
        console.log("[+] Total discovery scans performed: " + totalDiscoveryScans);
        console.log("[+] Discovery phase duration: " + ((discoveryCompleteTime - lastDiscoveryTime + DISCOVERY_TIMEOUT) / 1000).toFixed(1) + " seconds");

        if (discoveredInstances.size === 0) {
            console.log("[*] No collectible ruins found. Monitoring will continue for new instances.");
        } else {
            console.log("[+] ===== PHASE 2: BATCH COLLECTION STARTING =====");
            console.log("[*] Beginning batch processing of " + discoveredInstances.size + " instances");
            console.log("[*] Batch size: " + BATCH_SIZE + ", Interval: " + (BATCH_INTERVAL/1000) + " seconds");

            // Start batch processing immediately
            setTimeout(function() {
                processCollectionBatch();
            }, 10000);
        }
    }

    // Initialize discovery phase
    lastDiscoveryTime = Date.now();

    // Phase 2: Batch processing function (only runs after discovery is complete)
    function processCollectionBatch() {
        // Only process batches in Phase 2
        if (currentPhase !== 2) {
            return;
        }

        if (isProcessingBatch) {
            console.log("[*] PHASE 2: Batch processing already in progress, skipping...");
            return;
        }

        isProcessingBatch = true;
        batchNumber++;

        // Prepare current batch from discovered instances and failed retries
        currentBatch = [];
        var batchInstances = [];

        // Add discovered instances to batch
        var discoveredArray = Array.from(discoveredInstances.entries());
        for (var i = 0; i < Math.min(BATCH_SIZE, discoveredArray.length); i++) {
            var [instanceId, data] = discoveredArray[i];
            batchInstances.push({id: instanceId, data: data, source: 'discovered'});
            discoveredInstances.delete(instanceId);
        }

        // Add failed instances ready for retry
        var now = Date.now();
        var failedArray = Array.from(failedInstances.entries());
        var remainingBatchSlots = BATCH_SIZE - batchInstances.length;

        for (var i = 0; i < Math.min(remainingBatchSlots, failedArray.length); i++) {
            var [instanceId, failData] = failedArray[i];
            // Retry after 10 seconds and if retry count is under limit
            if (now - failData.lastFailTime > 10000 && failData.retryCount < RETRY_LIMIT) {
                batchInstances.push({id: instanceId, data: failData, source: 'retry'});
                failedInstances.delete(instanceId);
            }
        }

        if (batchInstances.length === 0) {
            // Check if all processing is complete
            var totalRemaining = discoveredInstances.size + failedInstances.size;
            if (totalRemaining === 0) {
                console.log("[+] ===== PHASE 2: BATCH COLLECTION COMPLETED =====");
                console.log("[+] All discovered instances have been processed!");
                console.log("[+] Final Statistics:");
                console.log("    - Total processed: " + processedInstances.size);
                console.log("    - Total failed (abandoned): " + Array.from(failedInstances.values()).filter(f => f.retryCount >= RETRY_LIMIT).length);
                console.log("    - Total batches: " + batchNumber);
            } else {
                console.log("[*] PHASE 2: Batch #" + batchNumber + " - No instances ready to process");
                console.log("[*] Remaining: " + discoveredInstances.size + " discovered, " + failedInstances.size + " failed");
            }
            isProcessingBatch = false;
            return;
        }

        console.log("[+] PHASE 2: Starting Batch #" + batchNumber + " - Processing " + batchInstances.length + " instances");
        console.log("[*] Batch composition: " + batchInstances.filter(b => b.source === 'discovered').length + " discovered, " +
                   batchInstances.filter(b => b.source === 'retry').length + " retries");
        console.log("[*] Remaining after this batch: " + (discoveredInstances.size + failedInstances.size - batchInstances.length) + " instances");

        var batchSuccesses = 0;
        var batchFailures = 0;

        // Process each instance in the batch
        batchInstances.forEach(function(batchItem, index) {
            setTimeout(function() {
                try {
                    var instanceId = batchItem.id;
                    var instanceData = batchItem.data;
                    var thisPtr = instanceData.ptr;

                    console.log("[*] Batch #" + batchNumber + " [" + (index + 1) + "/" + batchInstances.length + "] Processing: " + instanceId);

                    // CRITICAL: Re-discover this instance in real-time instead of using stale pointer
                    var freshPtr = null;
                    var freshInstanceFound = false;

                    console.log("[*] Re-scanning for fresh instance pointer (stale ptr was: " + thisPtr + ")");

                    // Use the Update() hook mechanism to find a fresh pointer for this instance
                    var tempDiscoveryCount = 0;
                    var maxDiscoveryAttempts = 100; // Limit discovery attempts

                    // Temporarily hook Update() to find fresh instances
                    var tempUpdateHook = Interceptor.attach(updateAddr, {
                        onEnter: function(args) {
                            if (freshInstanceFound || tempDiscoveryCount >= maxDiscoveryAttempts) {
                                return;
                            }

                            tempDiscoveryCount++;
                            var currentPtr = this.context.x0;

                            try {
                                var canCollectFunc = new NativeFunction(canCollectAddr, 'int', ['pointer']);
                                var result = canCollectFunc(currentPtr);

                                if (result === 1) {
                                    // Found a collectible instance - use this fresh pointer
                                    freshPtr = currentPtr;
                                    freshInstanceFound = true;
                                    console.log("[+] Found fresh collectible instance at: " + freshPtr);
                                }
                            } catch (e) {
                                // Ignore errors during fresh discovery
                            }
                        }
                    });

                    // Wait a moment for fresh discovery
                    setTimeout(function() {
                        tempUpdateHook.detach();

                        if (!freshInstanceFound || !freshPtr) {
                            console.log("[-] Could not find fresh instance pointer for " + instanceId + " after " + tempDiscoveryCount + " attempts");
                            batchFailures++;
                            return;
                        }

                        // Use the fresh pointer for processing
                        thisPtr = freshPtr;
                        console.log("[+] Using fresh pointer: " + thisPtr + " (discovered in " + tempDiscoveryCount + " attempts)");

                        // Validate fresh pointer
                        if (thisPtr.isNull() || thisPtr.equals(ptr(0))) {
                            console.log("[-] Fresh pointer is invalid for instance " + instanceId);
                            batchFailures++;
                            return;
                        }

                        // Verify fresh instance is still collectible
                        var canCollectFunc = new NativeFunction(canCollectAddr, 'int', ['pointer']);
                        var canCollect;

                        try {
                            canCollect = canCollectFunc(thisPtr);
                            console.log("[*] Fresh instance CanCollect() returned: " + canCollect);
                        } catch (e) {
                            console.log("[-] Error calling CanCollect() on fresh pointer " + thisPtr + ": " + e);
                            batchFailures++;
                            return;
                        }

                        if (canCollect !== 1) {
                            console.log("[*] Fresh instance no longer collectible, skipping");
                            batchSuccesses++; // Count as success since it's no longer needed
                            return;
                        }

                        // Safely get config and set cleanUp flag using fresh pointer
                        var configAddr = il2cpp.base.add(0x209B54C);  // Config() RVA
                        var configFunc = new NativeFunction(configAddr, 'pointer', ['pointer']);
                        var configPtr = null;

                        try {
                            configPtr = configFunc(thisPtr);
                            console.log("[*] Config() returned: " + configPtr + " for fresh instance");
                        } catch (e) {
                            console.log("[-] Error calling Config() on fresh pointer: " + e);
                            // Continue without setting cleanUp flag
                        }

                        if (configPtr && !configPtr.isNull() && !configPtr.equals(ptr(0))) {
                            try {
                                // Validate memory before writing
                                var cleanUpOffset = 0x30;
                                var cleanUpAddr = configPtr.add(cleanUpOffset);

                                // Try to read first to validate memory access
                                var currentValue = cleanUpAddr.readU8();
                                console.log("[*] Current cleanUp value: " + currentValue + " at " + cleanUpAddr);

                                // Set cleanUp flag to true
                                cleanUpAddr.writeU8(1);
                                console.log("[+] Set cleanUp flag for fresh instance");
                            } catch (e) {
                                console.log("[-] Error setting cleanUp flag: " + e);
                                // Continue with collection anyway
                            }
                        } else {
                            console.log("[-] Warning: Config() returned null/invalid pointer for fresh instance");
                        }

                        // Execute collection with error handling using fresh pointer
                        var finishCollectFunc = new NativeFunction(finishCollectAddr, 'void', ['pointer']);
                        try {
                            finishCollectFunc(thisPtr);
                            console.log("[+] FinishCollect() executed successfully on fresh pointer");
                        } catch (e) {
                            console.log("[-] Error calling FinishCollect() on fresh pointer: " + e);
                            batchFailures++;
                            return;
                        }

                        // Mark as successfully processed
                        processedInstances.add(instanceId);
                        batchSuccesses++;
                        console.log("[+] Batch #" + batchNumber + " [" + (index + 1) + "/" + batchInstances.length + "] SUCCESS: " + instanceId);

                    }, 100); // Wait 1 second for fresh discovery

                } catch (e) {
                    console.log("[-] Batch #" + batchNumber + " [" + (index + 1) + "/" + batchInstances.length + "] FAILED: " + batchItem.id + " - " + e);

                    // Add to failed instances for retry
                    var retryCount = (batchItem.data.retryCount || 0) + 1;
                    if (retryCount < RETRY_LIMIT) {
                        failedInstances.set(batchItem.id, {
                            ptr: batchItem.data.ptr,
                            lastFailTime: Date.now(),
                            retryCount: retryCount
                        });
                        console.log("[*] Added " + batchItem.id + " to retry queue (attempt " + retryCount + "/" + RETRY_LIMIT + ")");
                    } else {
                        console.log("[-] Instance " + batchItem.id + " exceeded retry limit, abandoning");
                    }
                    batchFailures++;
                }

                // Check if this is the last item in the batch
                if (index === batchInstances.length - 1) {
                    setTimeout(function() {
                        console.log("[+] Batch #" + batchNumber + " COMPLETED - Successes: " + batchSuccesses + ", Failures: " + batchFailures);
                        console.log("[*] Status - Processed: " + processedInstances.size + ", Pending: " + discoveredInstances.size + ", Failed: " + failedInstances.size);
                        isProcessingBatch = false;
                    }, 500);
                }
            }, index * 200); // 200ms delay between each instance in batch
        });
    }

    // Start batch processing timer (only runs in Phase 2)
    setInterval(function() {
        if (currentPhase === 2) {
            processCollectionBatch();
        }
    }, BATCH_INTERVAL);

    // Status monitoring and cleanup
    function printBatchStatus() {
        console.log("=== REAL-TIME COLLECTION STATUS ===");
        console.log("Collection Mode: REAL-TIME (immediate processing)");
        console.log("Progress:");
        console.log("  - Successfully processed: " + processedInstances.size);
        console.log("  - Failed instances: " + failedInstances.size);
        console.log("  - Total scans performed: " + totalDiscoveryScans);

        if (failedInstances.size > 0) {
            console.log("Failed instances details:");
            failedInstances.forEach(function(data, instanceId) {
                console.log("  " + instanceId + " - Retries: " + data.retryCount + "/" + RETRY_LIMIT);
            });
        }

        if (processedInstances.size >= 1000) {
            console.log("[+] TARGET ACHIEVED: All 1000 ruins have been processed!");
        } else {
            console.log("[*] Still processing... Target: 1000 ruins");
        }
        console.log("====================================");
    }

    // Cleanup old processed instances (prevent memory bloat)
    function cleanupProcessedInstances() {
        if (processedInstances.size > 1000) {
            console.log("[*] Cleaning up old processed instances...");
            var processedArray = Array.from(processedInstances);
            // Keep only the most recent 500
            processedInstances.clear();
            for (var i = processedArray.length - 500; i < processedArray.length; i++) {
                if (i >= 0) {
                    processedInstances.add(processedArray[i]);
                }
            }
            console.log("[+] Cleaned up processed instances, kept " + processedInstances.size);
        }
    }

    // Status monitoring timer (every 30 seconds)
    setInterval(printBatchStatus, 30000);

    // Cleanup timer (every 5 minutes)
    setInterval(cleanupProcessedInstances, 300000);

    // Manual control functions (accessible via rpc.exports)
    rpc.exports = {
        getBatchStatus: printBatchStatus,
        clearProcessedInstances: function() {
            processedInstances.clear();
            console.log("[+] Cleared all processed instances");
        },
        clearFailedInstances: function() {
            failedInstances.clear();
            console.log("[+] Cleared all failed instances");
        },
        forceBatchProcess: function() {
            if (currentPhase === 2) {
                console.log("[*] Forcing immediate batch processing...");
                processCollectionBatch();
            } else {
                console.log("[*] Cannot force batch processing - still in discovery phase");
            }
        },
        forceCompleteDiscovery: function() {
            if (currentPhase === 1) {
                console.log("[*] Forcing discovery phase completion...");
                completeDiscoveryPhase();
            } else {
                console.log("[*] Discovery phase already completed");
            }
        },
        resetToDiscovery: function() {
            console.log("[*] Resetting to discovery phase...");
            currentPhase = 1;
            discoveredInstances.clear();
            processedInstances.clear();
            failedInstances.clear();
            batchNumber = 0;
            isProcessingBatch = false;
            lastDiscoveryTime = Date.now();
            totalDiscoveryScans = 0;
            console.log("[+] Reset complete - discovery phase restarted");
        },
        setModule: function(moduleName) {
            console.log("[*] Attempting to manually set IL2CPP module to: " + moduleName);

            var foundModule = null;
            Process.enumerateModules().forEach(function(module) {
                if (module.name === moduleName) {
                    foundModule = module;
                }
            });

            if (foundModule) {
                il2cpp = foundModule;
                console.log("[+] Successfully set IL2CPP module to: " + il2cpp.name);
                console.log("[+] Module base: " + il2cpp.base);
                console.log("[+] Reinitializing hooks with new module...");

                // Reinitialize hooks with the new module
                initializeHooks();

                return true;
            } else {
                console.log("[-] Module '" + moduleName + "' not found");
                console.log("[-] Available modules:");
                Process.enumerateModules().forEach(function(module) {
                    if (module.size > 1000000) { // > 1MB
                        console.log("    " + module.name + " (" + (module.size/1024/1024).toFixed(1) + "MB)");
                    }
                });
                return false;
            }
        },
        listLargeModules: function() {
            console.log("[*] Large modules (>1MB) in process:");
            var modules = [];
            Process.enumerateModules().forEach(function(module) {
                if (module.size > 1000000) { // > 1MB
                    modules.push({
                        name: module.name,
                        size: module.size,
                        sizeMB: (module.size/1024/1024).toFixed(1),
                        base: module.base.toString()
                    });
                }
            });

            // Sort by size (largest first)
            modules.sort(function(a, b) { return b.size - a.size; });

            modules.forEach(function(mod, index) {
                console.log("  " + (index + 1) + ". " + mod.name + " (" + mod.sizeMB + "MB) at " + mod.base);
            });

            return modules;
        },
        getStats: function() {
            return {
                phase: currentPhase,
                discovered: discoveredInstances.size,
                processed: processedInstances.size,
                failed: failedInstances.size,
                batchNumber: batchNumber,
                isProcessing: isProcessingBatch,
                totalScans: totalDiscoveryScans,
                lastDiscovery: lastDiscoveryTime,
                discoveryComplete: discoveryCompleteTime
            };
        },
        autoUpgradeSelected: function() {
            console.log("[*] Manual trigger: Starting auto-upgrade for selected entities...");
            return autoUpgradeSelectedEntities();
        }
    };

    // Hook GetRewardType and GetRewardAmount for logging
    Interceptor.attach(il2cpp.base.add(0x209CC3C), {  // GetRewardType RVA
        onLeave: function(retval) {
            var rewardType = retval.toInt32();
            console.log("[*] Reward Type: " + rewardType);
        }
    });
    
    Interceptor.attach(il2cpp.base.add(0x209D9C4), {  // GetRewardAmount RVA
        onLeave: function(retval) {
            var amount = retval.toInt32();
            console.log("[*] Reward Amount: " + amount);
        }
    });
    
        console.log("[+] Ruins Auto-Collector hooks installed");
        console.log("[+] Will auto-collect any ruins when CanCollect() returns true");
    } // End of initializeHooks function
});

// Helper function to manually trigger collection on specific entity
function manualCollect(goodyHutAddress) {
    var goodyHutPtr = ptr(goodyHutAddress);

    // Use the same enhanced module detection logic
    var il2cpp = null;
    var potentialModules = [];

    Process.enumerateModules().forEach(function(module) {
        var moduleName = module.name.toLowerCase();

        // Check for obvious patterns first
        if (moduleName.includes("il2cpp") ||
            moduleName.includes("unity") ||
            moduleName.includes("libmain") ||
            moduleName.includes("domination") ||
            moduleName.includes("nexon")) {
            il2cpp = module;
            return;
        }

        // Collect potential candidates
        if (module.size > 5000000) { // > 5MB
            potentialModules.push(module);
        }
    });

    // If no obvious module found, try the largest potential candidate
    if (!il2cpp && potentialModules.length > 0) {
        potentialModules.sort(function(a, b) { return b.size - a.size; });
        il2cpp = potentialModules[0];
        console.log("[*] Using largest module as IL2CPP candidate: " + il2cpp.name);
    }

    if (!il2cpp) {
        console.log("[-] IL2CPP module not found for manual collection");
        return false;
    }

    try {
        // Validate pointer first
        if (goodyHutPtr.isNull() || goodyHutPtr.equals(ptr(0))) {
            console.log("[-] Invalid GoodyHutHelper pointer: " + goodyHutAddress);
            return false;
        }

        var canCollectFunc = new NativeFunction(il2cpp.base.add(0x209D258), 'int', ['pointer']);
        var finishCollectFunc = new NativeFunction(il2cpp.base.add(0x209B924), 'void', ['pointer']);
        var configFunc = new NativeFunction(il2cpp.base.add(0x209B54C), 'pointer', ['pointer']);

        // Check if collectible with error handling
        var canCollect;
        try {
            canCollect = canCollectFunc(goodyHutPtr);
        } catch (e) {
            console.log("[-] Error calling CanCollect(): " + e);
            return false;
        }

        if (canCollect === 1) {
            // Safely set cleanUp flag
            var configPtr = null;
            try {
                configPtr = configFunc(goodyHutPtr);
                console.log("[*] Manual collection - Config() returned: " + configPtr);
            } catch (e) {
                console.log("[-] Error calling Config(): " + e);
                // Continue without setting cleanUp flag
            }

            if (configPtr && !configPtr.isNull() && !configPtr.equals(ptr(0))) {
                try {
                    // Validate memory access before writing
                    var cleanUpAddr = configPtr.add(0x30);
                    var currentValue = cleanUpAddr.readU8();
                    console.log("[*] Manual collection - Current cleanUp value: " + currentValue);

                    cleanUpAddr.writeU8(1);
                    console.log("[+] Manual collection - Set cleanUp flag");
                } catch (e) {
                    console.log("[-] Error setting cleanUp flag: " + e);
                    // Continue with collection anyway
                }
            } else {
                console.log("[-] Manual collection - Config() returned null/invalid pointer");
            }

            // Execute collection
            try {
                finishCollectFunc(goodyHutPtr);
                console.log("[+] Manual collection completed for: " + goodyHutAddress);
                return true;
            } catch (e) {
                console.log("[-] Error calling FinishCollect(): " + e);
                return false;
            }
        } else {
            console.log("[-] Cannot collect ruins at: " + goodyHutAddress + " (CanCollect returned: " + canCollect + ")");
            return false;
        }
    } catch (e) {
        console.log("[-] Error in manual collection: " + e);
        return false;
    }
}

// Usage: manualCollect("0x12345678")  // Replace with actual GoodyHutHelper instance address

// EntityController Auto-Upgrade Function
// Iterates through all EntityController instances, checks if selected, then upgrades to max level
function autoUpgradeSelectedEntities() {
    console.log("[+] ===== STARTING AUTO-UPGRADE FOR SELECTED ENTITIES =====");

    // Enhanced module detection logic (same as manual collection)
    var il2cpp = null;
    var potentialModules = [];

    Process.enumerateModules().forEach(function(module) {
        var moduleName = module.name.toLowerCase();

        // Check for obvious patterns first
        if (moduleName.includes("il2cpp") ||
            moduleName.includes("unity") ||
            moduleName.includes("libmain") ||
            moduleName.includes("domination") ||
            moduleName.includes("nexon")) {
            il2cpp = module;
            return;
        }

        // Collect potential candidates
        if (module.size > 5000000) { // > 5MB
            potentialModules.push(module);
        }
    });

    // If no obvious module found, try the largest potential candidate
    if (!il2cpp && potentialModules.length > 0) {
        potentialModules.sort(function(a, b) { return b.size - a.size; });
        il2cpp = potentialModules[0];
        console.log("[*] Using largest module as IL2CPP candidate: " + il2cpp.name);
    }

    if (!il2cpp) {
        console.log("[-] IL2CPP module not found for auto-upgrade");
        return false;
    }

    console.log("[+] Using module: " + il2cpp.name + " at base: " + il2cpp.base);

    // Calculate actual addresses using module base + RVA offsets from user's memory
    var isSelectedAddr = il2cpp.base.add(0x1E53050);      // IsSelected() RVA
    var canUpgradeAddr = il2cpp.base.add(0x1E4027C);      // CanUpgrade(bool useAlternateResource = False) RVA
    var getMaxLevelAddr = il2cpp.base.add(0x1E4A0B8);     // GetMaxUpgradeLevel() RVA
    var getLevelAddr = null;                              // GetLevel() RVA - need to find this
    var instantUpgradeAddr = null;                        // InstantUpgrade() RVA - need to find this

    console.log("[*] IsSelected address: " + isSelectedAddr);
    console.log("[*] CanUpgrade address: " + canUpgradeAddr);
    console.log("[*] GetMaxUpgradeLevel address: " + getMaxLevelAddr);

    // Try to find GetLevel() and InstantUpgrade() methods by scanning nearby RVAs
    // These are likely close to the other EntityController methods
    var potentialGetLevelRVAs = [
        0x1E4A000, 0x1E4A100, 0x1E4A200, 0x1E4A300, 0x1E4A400, 0x1E4A500,
        0x1E49000, 0x1E49100, 0x1E49200, 0x1E49300, 0x1E49400, 0x1E49500,
        0x1E48000, 0x1E48100, 0x1E48200, 0x1E48300, 0x1E48400, 0x1E48500
    ];

    var potentialInstantUpgradeRVAs = [
        0x1E50000, 0x1E50100, 0x1E50200, 0x1E50300, 0x1E50400, 0x1E50500,
        0x1E51000, 0x1E51100, 0x1E51200, 0x1E51300, 0x1E51400, 0x1E51500,
        0x1E52000, 0x1E52100, 0x1E52200, 0x1E52300, 0x1E52400, 0x1E52500
    ];

    // Use the correct RVA for InstantUpgrade from the provided information
    getLevelAddr = il2cpp.base.add(0x1E4A000);        // Placeholder - still needs correct RVA for GetLevel
    instantUpgradeAddr = il2cpp.base.add(0x1E40540);  // InstantUpgrade() RVA from provided info

    console.log("[*] GetLevel address (placeholder): " + getLevelAddr);
    console.log("[*] InstantUpgrade address (placeholder): " + instantUpgradeAddr);

    // Create function pointers
    var isSelectedFunc = new NativeFunction(isSelectedAddr, 'int', ['pointer']);
    var canUpgradeFunc = new NativeFunction(canUpgradeAddr, 'int', ['pointer', 'int']); // bool useAlternateResource parameter
    var getMaxLevelFunc = new NativeFunction(getMaxLevelAddr, 'int', ['pointer']);
    var getLevelFunc = new NativeFunction(getLevelAddr, 'int', ['pointer']);
    var instantUpgradeFunc = new NativeFunction(instantUpgradeAddr, 'void', ['pointer']);

    // Discover all EntityController instances using the same proven pattern as successful functions
    console.log("[*] Discovering EntityController instances using proven Il2Cpp.gc.choose() pattern...");

    try {
        var entityInstances = [];
        var EntityControllerClass = null;

        // Enhanced diagnostics for Il2Cpp access
        console.log("[*] Diagnostic: Checking Il2Cpp availability...");
        console.log("[*] typeof Il2Cpp: " + typeof Il2Cpp);

        if (typeof Il2Cpp !== 'undefined') {
            console.log("[*] Il2Cpp is defined");
            console.log("[*] Il2Cpp.domain: " + (Il2Cpp.domain ? "available" : "not available"));

            if (Il2Cpp.domain) {
                console.log("[*] Il2Cpp.domain.assemblies: " + (Il2Cpp.domain.assemblies ? "available" : "not available"));

                // List available assemblies for debugging
                try {
                    var assemblies = Il2Cpp.domain.assemblies;
                    console.log("[*] Available assemblies: " + assemblies.length);
                    for (var i = 0; i < Math.min(assemblies.length, 5); i++) {
                        console.log("    - " + assemblies[i].name);
                    }
                } catch (e) {
                    console.log("[*] Error listing assemblies: " + e);
                }
            }
        } else {
            console.log("[-] Il2Cpp is not defined in this context");
        }

        // Method 1: Try the exact same approach as successful functions
        try {
            console.log("[*] Method 1: Attempting Il2Cpp.domain.assembly approach...");
            var AssemblyCSharp = Il2Cpp.domain.assembly("Assembly-CSharp").image;
            EntityControllerClass = AssemblyCSharp.class("EntityController");

            if (EntityControllerClass) {
                console.log("[+] Method 1 SUCCESS: Found EntityController class via Il2Cpp domain");
            } else {
                console.log("[-] Method 1: EntityController class not found in Assembly-CSharp");
            }
        } catch (e) {
            console.log("[-] Method 1 FAILED: " + e);
        }

        // Method 2: Try alternative assembly names if Method 1 failed
        if (!EntityControllerClass) {
            console.log("[*] Method 2: Trying alternative assembly names...");
            var alternativeAssemblies = ["mscorlib", "UnityEngine", "UnityEngine.CoreModule", "Assembly-CSharp-firstpass"];

            for (var i = 0; i < alternativeAssemblies.length; i++) {
                try {
                    var assembly = Il2Cpp.domain.assembly(alternativeAssemblies[i]);
                    if (assembly && assembly.image) {
                        var testClass = assembly.image.class("EntityController");
                        if (testClass) {
                            EntityControllerClass = testClass;
                            console.log("[+] Method 2 SUCCESS: Found EntityController in " + alternativeAssemblies[i]);
                            break;
                        }
                    }
                } catch (e) {
                    console.log("[*] Method 2: " + alternativeAssemblies[i] + " failed: " + e);
                }
            }
        }

        // Method 3: Use the same pattern as the working discoverInstances() function
        if (!EntityControllerClass) {
            console.log("[*] Method 3: Using working discoverInstances() pattern...");
            try {
                // This replicates the exact pattern from the working functions
                // Based on the existing codebase pattern that successfully finds 4000+ instances

                // Try to access the entityControllerClass that was set during initialization
                if (typeof this.entityControllerClass !== 'undefined' && this.entityControllerClass) {
                    EntityControllerClass = this.entityControllerClass;
                    console.log("[+] Method 3 SUCCESS: Using initialized entityControllerClass");
                } else {
                    console.log("[*] Method 3: entityControllerClass not available in this context");
                }
            } catch (e) {
                console.log("[-] Method 3 FAILED: " + e);
            }
        }

        // Method 4: Direct Il2Cpp.gc.choose with class discovery
        if (!EntityControllerClass) {
            console.log("[*] Method 4: Attempting direct class discovery...");
            try {
                // Try to find EntityController instances directly without explicit class reference
                // This mimics the pattern used in successful batch processing functions

                // Get all assemblies and search for EntityController
                var assemblies = Il2Cpp.domain.assemblies;
                for (var i = 0; i < assemblies.length; i++) {
                    try {
                        var assembly = assemblies[i];
                        if (assembly.image) {
                            var classes = assembly.image.classes;
                            for (var j = 0; j < classes.length; j++) {
                                if (classes[j].name === "EntityController") {
                                    EntityControllerClass = classes[j];
                                    console.log("[+] Method 4 SUCCESS: Found EntityController in " + assembly.name);
                                    break;
                                }
                            }
                            if (EntityControllerClass) break;
                        }
                    } catch (e) {
                        // Continue searching
                    }
                }
            } catch (e) {
                console.log("[-] Method 4 FAILED: " + e);
            }
        }

        // If we found the EntityController class, use Il2Cpp.gc.choose()
        if (EntityControllerClass) {
            console.log("[+] EntityController class found! Using Il2Cpp.gc.choose()...");
            var rawInstances = Il2Cpp.gc.choose(EntityControllerClass);

            console.log("[+] Il2Cpp.gc.choose() found " + rawInstances.length + " EntityController instances");

            // Convert to our format (same as successful functions)
            for (var i = 0; i < rawInstances.length; i++) {
                var instance = rawInstances[i];
                if (instance && instance.handle && !instance.handle.isNull() && !instance.handle.equals(ptr(0))) {
                    entityInstances.push({
                        id: instance.handle.toString(),
                        ptr: instance.handle
                    });
                }
            }

        } else {
            console.log("[-] All methods failed to find EntityController class");
            console.log("[*] Implementing fallback discovery using memory scanning...");

            // Method 5: Fallback using the same Update() hook pattern that works in other parts
            console.log("[*] Method 5: Using Update() hook fallback (same as working batch processing)...");

            var updateAddr = il2cpp.base.add(0x209CF60);  // Update() RVA
            var discoveryAttempts = 0;
            var maxDiscoveryAttempts = 200; // Increased attempts
            var discoveredPointers = new Set(); // Avoid duplicates

            console.log("[*] Starting Update() hook discovery for 10 seconds...");

            var tempUpdateHook = Interceptor.attach(updateAddr, {
                onEnter: function(args) {
                    if (discoveryAttempts >= maxDiscoveryAttempts) {
                        return;
                    }

                    discoveryAttempts++;
                    var entityPtr = this.context.x0;

                    // Validate pointer
                    if (entityPtr && !entityPtr.isNull() && !entityPtr.equals(ptr(0))) {
                        var instanceId = entityPtr.toString();

                        if (!discoveredPointers.has(instanceId)) {
                            discoveredPointers.add(instanceId);
                            entityInstances.push({
                                id: instanceId,
                                ptr: entityPtr
                            });

                            if (entityInstances.length % 50 === 0) {
                                console.log("[*] Fallback discovered " + entityInstances.length + " instances...");
                            }
                        }
                    }
                }
            });

            // Wait for discovery (synchronous approach)
            var startTime = Date.now();
            var discoveryTimeout = 10000; // 10 seconds

            while (Date.now() - startTime < discoveryTimeout && discoveryAttempts < maxDiscoveryAttempts) {
                // Busy wait to allow hook to collect instances
                Java.perform(function() {
                    // Small delay to allow hook execution
                });
            }

            tempUpdateHook.detach();
            console.log("[+] Fallback discovery completed: Found " + entityInstances.length + " instances via Update() hook");
        }

        console.log("[+] Discovery completed: Found " + entityInstances.length + " EntityController instances");

        if (entityInstances.length === 0) {
            console.log("[-] No EntityController instances found. This could indicate:");
            console.log("    1. Il2Cpp domain is not properly initialized");
            console.log("    2. EntityController class name might be different");
            console.log("    3. Game hasn't fully loaded yet");
            console.log("    4. Assembly-CSharp image is not accessible");
            return false;
        }

        console.log("[*] Starting upgrade process...");

        var selectedCount = 0;
        var upgradableCount = 0;
        var upgradedCount = 0;
        var errorCount = 0;

        // Process each EntityController instance
        entityInstances.forEach(function(entityData, index) {
            try {
                var entityPtr = entityData.ptr;
                var instanceId = entityData.id;

                // Step 1: Check if entity is selected
                var isSelected;
                try {
                    isSelected = isSelectedFunc(entityPtr);
                } catch (e) {
                    console.log("[-] Error calling IsSelected() on " + instanceId + ": " + e);
                    errorCount++;
                    return;
                }

                if (isSelected !== 1) {
                    // Entity is not selected, skip
                    return;
                }

                selectedCount++;
                console.log("[+] Entity " + index + " (" + instanceId + ") is SELECTED");

                // Step 2: Check if entity can be upgraded
                var canUpgrade;
                try {
                    canUpgrade = canUpgradeFunc(entityPtr, 0); // useAlternateResource = false
                } catch (e) {
                    console.log("[-] Error calling CanUpgrade() on " + instanceId + ": " + e);
                    errorCount++;
                    return;
                }

                if (canUpgrade !== 1) {
                    console.log("[*] Entity " + index + " (" + instanceId + ") cannot be upgraded");
                    return;
                }

                upgradableCount++;
                console.log("[+] Entity " + index + " (" + instanceId + ") CAN BE UPGRADED");

                // Step 3: Get current level and max level
                var currentLevel, maxLevel;
                try {
                    currentLevel = getLevelFunc(entityPtr);
                    maxLevel = getMaxLevelFunc(entityPtr);
                } catch (e) {
                    console.log("[-] Error getting levels for " + instanceId + ": " + e);
                    errorCount++;
                    return;
                }

                console.log("[*] Entity " + index + " (" + instanceId + ") - Current Level: " + currentLevel + ", Max Level: " + maxLevel);

                // Step 4: Upgrade until max level is reached
                if (currentLevel >= maxLevel) {
                    console.log("[*] Entity " + index + " (" + instanceId + ") is already at max level");
                    return;
                }

                var upgradeAttempts = 0;
                var maxUpgradeAttempts = maxLevel - currentLevel + 5; // Safety limit

                while (currentLevel < maxLevel && upgradeAttempts < maxUpgradeAttempts) {
                    try {
                        // Call InstantUpgrade()
                        instantUpgradeFunc(entityPtr);
                        upgradeAttempts++;

                        // Get new current level
                        var newLevel = getLevelFunc(entityPtr);

                        if (newLevel > currentLevel) {
                            currentLevel = newLevel;
                            console.log("[+] Entity " + index + " upgraded to level " + currentLevel + "/" + maxLevel);
                        } else {
                            console.log("[*] Entity " + index + " upgrade attempt " + upgradeAttempts + " - level unchanged");
                            break; // Prevent infinite loop if upgrade fails
                        }

                        // Small delay between upgrades
                        if (currentLevel < maxLevel) {
                            // Use a synchronous delay (busy wait) since we're in a forEach callback
                            var delayStart = Date.now();
                            while (Date.now() - delayStart < 100) {
                                // 100ms delay
                            }
                        }

                    } catch (e) {
                        console.log("[-] Error during upgrade attempt " + upgradeAttempts + " for " + instanceId + ": " + e);
                        errorCount++;
                        break;
                    }
                }

                if (currentLevel >= maxLevel) {
                    upgradedCount++;
                    console.log("[+] Entity " + index + " (" + instanceId + ") FULLY UPGRADED to level " + currentLevel);
                } else {
                    console.log("[-] Entity " + index + " (" + instanceId + ") upgrade incomplete - stopped at level " + currentLevel + "/" + maxLevel);
                }

            } catch (e) {
                console.log("[-] Error processing entity " + index + " (" + entityData.id + "): " + e);
                errorCount++;
            }
        });

        // Print final statistics
        console.log("[+] ===== AUTO-UPGRADE COMPLETED =====");
        console.log("[*] Total entities discovered: " + entityInstances.length);
        console.log("[*] Selected entities: " + selectedCount);
        console.log("[*] Upgradable entities: " + upgradableCount);
        console.log("[*] Fully upgraded entities: " + upgradedCount);
        console.log("[*] Errors encountered: " + errorCount);
        console.log("[+] =====================================");

    } catch (discoveryError) {
        console.log("[-] Error during EntityController discovery: " + discoveryError);
        console.log("[-] Auto-upgrade process aborted");
        return false;
    }

    return true;
}